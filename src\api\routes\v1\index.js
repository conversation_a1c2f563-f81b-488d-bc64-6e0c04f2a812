/* eslint-disable max-len */
const express = require('express');
const { uploadDir } = require('../../../config/vars');
// const userRoutes = require('../../module/user/routes/v1/user.route');
const authRoutes = require('../../module/user/routes/v1/auth.route');
const uploadRoutes = require('../../module/extra/routes/v1/upload.route');
const departmentRoutes = require('../../module/department/routes/v1/department.route');
const positionRoutes = require('../../module/department/routes/v1/position.route');
// const planRoutes = require('../../module/plan/routes/v1/plan.route');
// const jobDescriptionRoutes = require('../../module/plan/routes/v1/job_description.route');
// const jobDescriptionItemRoutes = require('../../module/plan/routes/v1/job_description_item.route');
// const workforceRoutes = require('../../module/plan/routes/v1/workforce.route');
// const recruitmentRequestRoutes = require('../../module/plan/routes/v1/recruitment_request.route');
const contractRoutes = require('../../module/contract/routes/v1/contract.route');
const userPermissionInterfaceRoutes = require('../../module/user/routes/v1/user.permission.interface.route');
const userPermissionInterfaceFunctionRoutes = require('../../module/user/routes/v1/user.permission.interface.function.route');
const userPermissionGroupRoutes = require('../../module/user/routes/v1/user.permission.group.route');
const employeeRoutes = require('../../module/employee/routes/v1/employee.route');
const employeePositionRoutes = require('../../module/employee/routes/v1/employee.position.route');
const leaveRoutes = require('../../module/employee/routes/v1/leave.route');
const overtimeRoutes = require('../../module/employee/routes/v1/overtime.route');
const articleRoutes = require('../../module/article/routes/v1/article.route');
const categoryRoutes = require('../../module/article/routes/v1/article.category.route');
const folderRoutes = require('../../module/article/routes/v1/article.folder.route');
const typeRoutes = require('../../module/article/routes/v1/article.type.route');
const filedRoutes = require('../../module/article/routes/v1/article.field.route');
const articleDepartmentRoutes = require('../../module/article/routes/v1/article.department.route');
const workScheduleRoutes = require('../../module/work/routes/v1/work.schedule.route');
const userPermissionRoutes = require('../../module/user/routes/v1/user.permission.route');
const carRoutes = require('../../module/car/routes/v1/car.route');
const carScheduleRoutes = require('../../module/car/routes/v1/car.schedule.route');
const assignmentRoutes = require('../../module/assignment/routes/v1/assignment.route');
const assignmentContentRoutes = require('../../module/assignment/routes/v1/assignment.content.route');
const dashboardRoutes = require('../../module/dashboard/routes/v1/dashboard.route');
const notificationRoutes = require('../../module/notification/routes/v1/notification.route');
const incomeAgreementRoutes = require('../../module/contract/routes/v1/income.agreement.route');
const workdayRoutes = require('../../module/employee/routes/v1/workday.route');
const paycheckRoutes = require('../../module/paycheck/routes/v1/paycheck.route');
const deviceRoutes = require('../../module/device/routes/v1/device.route');
const deviceNotebookRoutes = require('../../module/device/routes/v1/device.notebook.route');
const studentRoutes = require('../../module/student/routes/v1/student.route');
const enrollRoutes = require('../../module/student/routes/v1/enroll.route');
const attendanceRoutes = require('../../module/student/routes/v1/attendance.route');
const attendancePermissionRoutes = require('../../module/student/routes/v1/attendance.permission.route');
const classRoutes = require('../../module/student/routes/v1/class.route');
const absentRoutes = require('../../module/student/routes/v1/absent.route');
const timekeepingRoutes = require('../../module/employee/routes/v1/timekeeping.route');
const workCalenderRoutes = require('../../module/work/routes/v1/work.calendar.route');
const workReportRoutes = require('../../module/work/routes/v1/work.report.route');
const calendarRoutes = require('../../module/student/routes/v1/calendar.route');
const employeeFileRoutes = require('../../module/employee/routes/v1/employee.file.route');
const certificateRoutes = require('../../module/student/routes/v1/certificate.route');
const componentScoreRoutes = require('../../module/student/routes/v1/component.score.route');
const gradeComponentsRoutes = require('../../module/student/routes/v1/grade.components.route');
const studentGradeRoutes = require('../../module/student/routes/v1/student.grade.route');

const router = express.Router();

/**
 * GET v1/status
 */
router.get('/status', (req, res) => res.send('OK'));

/**
 * GET v1/docs
 */
router.use('/docs', express.static('docs'));

/**
 * GET v1/static
 */
router.use('/static', express.static(`${uploadDir.postAvatar}`));

// router.use('/users', userRoutes);
router.use('/auth', authRoutes);

router.use('/extra/upload', uploadRoutes);
router.use('/extra/dashboard', dashboardRoutes);
router.use('/notification/notification', notificationRoutes);

router.use('/department/department', departmentRoutes);
router.use('/department/position', positionRoutes);

// router.use(route.plan, planRoutes);
// router.use(route.jobDescription, jobDescriptionRoutes);
// router.use(route.jobDescriptionItem, jobDescriptionItemRoutes);
// router.use(route.workforce, workforceRoutes);
// router.use(route.recruitmentRequest, recruitmentRequestRoutes);
router.use('/contract/contract', contractRoutes);
router.use('/contract/income-agreement', incomeAgreementRoutes);
router.use('/contract/income-agreement', incomeAgreementRoutes);
router.use('/user/permission/interface', userPermissionInterfaceRoutes);
router.use('/user/permission/interface-function', userPermissionInterfaceFunctionRoutes);
router.use('/user/permission/group', userPermissionGroupRoutes);
router.use('/user/permission', userPermissionRoutes);

router.use('/employee/employee', employeeRoutes);
router.use('/employee/leave', leaveRoutes);
router.use('/employee/overtime', overtimeRoutes);
router.use('/employee/workday', workdayRoutes);
router.use('/employee/position', employeePositionRoutes);
router.use('/employee/timekeeping', timekeepingRoutes);
router.use('/employee/file', employeeFileRoutes);


router.use('/article/article', articleRoutes);
router.use('/article/category', categoryRoutes);
router.use('/article/folder', folderRoutes);
router.use('/article/type', typeRoutes);
router.use('/article/field', filedRoutes);
router.use('/article/department', articleDepartmentRoutes);

router.use('/work/schedule', workScheduleRoutes);
router.use('/work/calendar', workCalenderRoutes);
router.use('/work/report', workReportRoutes);


router.use('/car/car', carRoutes);
router.use('/car/schedule', carScheduleRoutes);

router.use('/assignment/assignment', assignmentRoutes);
router.use('/assignment/content', assignmentContentRoutes);

router.use('/payslip/payslip', paycheckRoutes);

router.use('/device/device', deviceRoutes);
router.use('/device/notebook', deviceNotebookRoutes);

router.use('/student/student', studentRoutes);
router.use('/student/enroll', enrollRoutes);
router.use('/student/class', classRoutes);
router.use('/student/attendance-permission', attendancePermissionRoutes);
router.use('/student/attendance', attendanceRoutes);
router.use('/student/absent', absentRoutes);
router.use('/student/calendar', calendarRoutes);
router.use('/student/certificate', certificateRoutes);
router.use('/student/component-scores', componentScoreRoutes);
router.use('/student/grade-components', gradeComponentsRoutes);
router.use('/student/student-grades', studentGradeRoutes);

module.exports = router;
