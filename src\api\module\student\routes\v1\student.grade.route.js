// Student Grade Routes
const express = require('express');
const { validate } = require('express-validation');
const controller = require('../../controllers/student.grade.controller');
const { authorize } = require('../../../../middlewares/auth');
const {
  createStudentGrade,
  listStudentGrades,
  updateStudentGrade,
  deleteStudentGrade,
  getStudentGrade,
  getStudentGradesByStudent,
  getStudentGradesByClass,
} = require('../../validations/student.grade.validation');

const router = express.Router();

/**
 * Load student grade when API with id route parameter is hit
 */
router.param('id', controller.load);

/**
 * @api {get} v1/student/student-grades List Student Grades
 * @apiDescription Get list of student grades
 * @apiVersion 1.0.0
 * @apiName ListStudentGrades
 * @apiGroup StudentGrade
 * @apiPermission user
 *
 * @apiHeader {String} Authorization User's access token
 *
 * @apiParam  {Number{1-}}         [page=1]        List page
 * @apiParam  {Number{1-100}}      [perPage=30]    Per page
 * @apiParam  {String}             [order_by]      Order by field
 * @apiParam  {String=asc,desc}    [order_way]     Order direction
 * @apiParam  {Number}             [student]       Student ID
 * @apiParam  {Number}             [year]          Academic year
 * @apiParam  {Number}             [semester]      Semester
 * @apiParam  {Number}             [course]        Course ID
 * @apiParam  {Number}             [_class]        Class ID
 * @apiParam  {Number}             [department]    Department ID
 * @apiParam  {Number}             [major]         Major ID
 *
 * @apiSuccess {Number} total Total student grades
 * @apiSuccess {Object[]} data List of student grades
 */

/**
 * @api {post} v1/student/student-grades Create Student Grade
 * @apiDescription Create a new student grade
 * @apiVersion 1.0.0
 * @apiName CreateStudentGrade
 * @apiGroup StudentGrade
 * @apiPermission lecturer
 *
 * @apiHeader {String} Authorization User's access token
 *
 * @apiParam  {Number}     year           Academic year (2000-2100)
 * @apiParam  {Number}     semester       Semester (1-3)
 * @apiParam  {Number}     department     Department ID
 * @apiParam  {Number}     major          Major ID
 * @apiParam  {Number}     class          Class ID
 * @apiParam  {Number}     student        Student ID
 * @apiParam  {Number}     course         Course ID
 * @apiParam  {Number}     [grade_100]    Grade in 10-point scale (0-10)
 * @apiParam  {Number}     [grade_4]      Grade in 4-point scale (0-4)
 * @apiParam  {String}     [grade_letter] Letter grade (A+, A, B+, B, C+, C, D+, D, F)
 * @apiParam  {String}     [grade_10]     Grade as string
 *
 * @apiSuccess {Object} studentGrade Created student grade
 */
router
  .route('/')
  .get(authorize(), validate(listStudentGrades), controller.list)
  .post(authorize(), validate(createStudentGrade), controller.create);

/**
 * @api {get} v1/student/student-grades/:id Get Student Grade
 * @apiDescription Get student grade information
 * @apiVersion 1.0.0
 * @apiName GetStudentGrade
 * @apiGroup StudentGrade
 * @apiPermission user
 *
 * @apiHeader {String} Authorization User's access token
 *
 * @apiSuccess {Number}  _id           Grade ID
 * @apiSuccess {Number}  year          Academic year
 * @apiSuccess {Number}  semester      Semester
 * @apiSuccess {Number}  department    Department ID
 * @apiSuccess {Number}  major         Major ID
 * @apiSuccess {Number}  class         Class ID
 * @apiSuccess {Number}  student       Student ID
 * @apiSuccess {Number}  course        Course ID
 * @apiSuccess {Number}  grade_100     Grade in 10-point scale
 * @apiSuccess {Number}  grade_4       Grade in 4-point scale
 * @apiSuccess {String}  grade_letter  Letter grade
 * @apiSuccess {String}  grade_10      Grade as string
 * @apiSuccess {Date}    created_at    Creation timestamp
 * @apiSuccess {Date}    updated_at    Update timestamp
 *
 * @apiError (Not Found 404) NotFound Student grade does not exist
 */

/**
 * @api {patch} v1/student/student-grades/:id Update Student Grade
 * @apiDescription Update student grade
 * @apiVersion 1.0.0
 * @apiName UpdateStudentGrade
 * @apiGroup StudentGrade
 * @apiPermission lecturer
 *
 * @apiHeader {String} Authorization User's access token
 *
 * @apiParam  {Number}     [year]         Academic year (2000-2100)
 * @apiParam  {Number}     [semester]     Semester (1-3)
 * @apiParam  {Number}     [department]   Department ID
 * @apiParam  {Number}     [major]        Major ID
 * @apiParam  {Number}     [class]        Class ID
 * @apiParam  {Number}     [student]      Student ID
 * @apiParam  {Number}     [course]       Course ID
 * @apiParam  {Number}     [grade_100]    Grade in 10-point scale (0-10)
 * @apiParam  {Number}     [grade_4]      Grade in 4-point scale (0-4)
 * @apiParam  {String}     [grade_letter] Letter grade (A+, A, B+, B, C+, C, D+, D, F)
 * @apiParam  {String}     [grade_10]     Grade as string
 *
 * @apiSuccess (No Content 204) Successfully updated
 *
 * @apiError (Unauthorized 401) Unauthorized Only authenticated users can update grades
 * @apiError (Forbidden 403) Forbidden Only lecturer of the class can update grades
 * @apiError (Not Found 404) NotFound Student grade does not exist
 */

/**
 * @api {delete} v1/student/student-grades/:id Delete Student Grade
 * @apiDescription Delete a student grade
 * @apiVersion 1.0.0
 * @apiName DeleteStudentGrade
 * @apiGroup StudentGrade
 * @apiPermission lecturer
 *
 * @apiHeader {String} Authorization User's access token
 *
 * @apiSuccess (No Content 204) Successfully deleted
 *
 * @apiError (Unauthorized 401) Unauthorized Only authenticated users can delete grades
 * @apiError (Forbidden 403) Forbidden Only lecturer of the class can delete grades
 * @apiError (Not Found 404) NotFound Student grade does not exist
 */
router
  .route('/:id')
  .get(authorize(), validate(getStudentGrade), controller.get)
  .patch(authorize(), validate(updateStudentGrade), controller.update)
  .delete(authorize(), validate(deleteStudentGrade), controller.remove);

/**
 * @api {get} v1/student/student-grades/student/:studentId Get Grades by Student
 * @apiDescription Get all grades for a specific student
 * @apiVersion 1.0.0
 * @apiName GetStudentGradesByStudent
 * @apiGroup StudentGrade
 * @apiPermission user
 *
 * @apiHeader {String} Authorization User's access token
 *
 * @apiParam  {Number}     studentId    Student ID
 * @apiParam  {Number}     [year]       Academic year filter
 * @apiParam  {Number}     [semester]   Semester filter
 *
 * @apiSuccess {Number} total Total grades for the student
 * @apiSuccess {Object[]} data List of student's grades
 */
router
  .route('/student/:studentId')
  .get(authorize(), validate(getStudentGradesByStudent), controller.getByStudent);

/**
 * @api {get} v1/student/student-grades/class/:classId Get Grades by Class
 * @apiDescription Get all grades for a specific class
 * @apiVersion 1.0.0
 * @apiName GetStudentGradesByClass
 * @apiGroup StudentGrade
 * @apiPermission lecturer
 *
 * @apiHeader {String} Authorization User's access token
 *
 * @apiParam  {Number}     classId      Class ID
 * @apiParam  {Number}     [year]       Academic year filter
 * @apiParam  {Number}     [semester]   Semester filter
 * @apiParam  {Number}     [course]     Course filter
 *
 * @apiSuccess {Number} total Total grades for the class
 * @apiSuccess {Object[]} data List of class grades
 */
router
  .route('/class/:classId')
  .get(authorize(), validate(getStudentGradesByClass), controller.getByClass);

module.exports = router;
