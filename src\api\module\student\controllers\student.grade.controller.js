// Student Grade Controller
const _ = require('lodash');
const moment = require('moment');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const db = require('../../../../config/mysql');
const { handler: errorHandler } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { fields } = require('../models/student.grade.model');

const StudentGrade = db.studentGrade;
const Class = db.class;

/**
 * Load student grade and append to req.
 * @public
 */
exports.load = async (req, res, next, id) => {
  try {
    const studentGrade = await StudentGrade.get(id);
    req.locals = { studentGrade };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

/**
 * Get student grade
 * @public
 */
exports.get = (req, res) => {
  jsonSuccess(req.locals.studentGrade, req, res);
};

/**
 * Create new student grade
 * @public
 */
exports.create = async (req, res, next) => {
  try {
    const { user, body } = req;

    // Check if user has permission to create grades for this class
    const _class = await Class.get(body.class);
    if (_class.dataValues.lecturer !== req.user._id) {
      throw new APIError({
        message: 'Don\'t have permission to create grades for this class',
        status: httpStatus.FORBIDDEN,
      });
    }

    // Check if grade already exists for this student, course, year, semester
    const existingGrade = await StudentGrade.findOne({
      where: {
        [fields.student]: body.student,
        [fields.course]: body.course,
        [fields.year]: body.year,
        [fields.semester]: body.semester,
        [fields.deleted_at]: null,
      },
    });

    if (existingGrade) {
      throw new APIError({
        message: 'Grade already exists for this student in this course',
        status: httpStatus.CONFLICT,
      });
    }

    // Create new grade
    req.body.created_by = user.email;
    const dbData = {};
    _.forEach(body, (value, key) => {
      dbData[fields[key]] = value;
    });

    const studentGrade = StudentGrade.build({
      ...dbData,
    });
    const saved = await studentGrade.save();
    jsonSuccess(saved, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Update existing student grade
 * @public
 */
exports.update = async (req, res, next) => {
  try {
    // Check if user has permission to update grades for this class
    const _class = await Class.get(req.locals.studentGrade.dataValues.class);
    if (_class.dataValues.lecturer !== req.user._id) {
      throw new APIError({
        message: 'Don\'t have permission to update grades for this class',
        status: httpStatus.FORBIDDEN,
      });
    }

    const { user } = req;
    const data = req.body;
    data.updated_by = user.email;
    data.updated_at = moment().format();

    await StudentGrade.patch({
      id: req.params.id,
      data: req.body,
    });

    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Get list of student grades
 * @public
 */
exports.list = async (req, res, next) => {
  try {
    const studentGrades = await StudentGrade.list(req.query);
    jsonSuccess(studentGrades, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete student grade
 * @public
 */
exports.remove = async (req, res, next) => {
  try {
    // Check if user has permission to delete grades for this class
    const _class = await Class.get(req.locals.studentGrade.dataValues.class);
    if (_class.dataValues.lecturer !== req.user._id) {
      throw new APIError({
        message: 'Don\'t have permission to delete grades for this class',
        status: httpStatus.FORBIDDEN,
      });
    }

    const result = await StudentGrade.remove({
      id: req.params.id,
      email: req.user.email,
    });

    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Get grades by student ID
 * @public
 */
exports.getByStudent = async (req, res, next) => {
  try {
    const { studentId } = req.params;
    const { year, semester } = req.query;

    const query = {
      student: studentId,
      deleted_at: null,
    };

    if (year) query.year = year;
    if (semester) query.semester = semester;

    const studentGrades = await StudentGrade.list(query);
    jsonSuccess(studentGrades, req, res);
  } catch (error) {
    next(error);
  }
};

/**
 * Get grades by class ID
 * @public
 */
exports.getByClass = async (req, res, next) => {
  try {
    const { classId } = req.params;
    const { year, semester, course } = req.query;

    // Check if user has permission to view grades for this class
    const _class = await Class.get(classId);
    if (_class.dataValues.lecturer !== req.user._id) {
      throw new APIError({
        message: 'Don\'t have permission to view grades for this class',
        status: httpStatus.FORBIDDEN,
      });
    }

    const query = {
      _class: classId,
      deleted_at: null,
    };

    if (year) query.year = year;
    if (semester) query.semester = semester;
    if (course) query.course = course;

    const studentGrades = await StudentGrade.list(query);
    jsonSuccess(studentGrades, req, res);
  } catch (error) {
    next(error);
  }
};

