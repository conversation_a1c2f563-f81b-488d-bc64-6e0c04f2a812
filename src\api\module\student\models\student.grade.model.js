const _ = require('lodash');
const { Op } = require('sequelize');
const moment = require('moment');

const fields = {
  table: 'v500',
  _id: 'pv500',
  year: 'fh050', // Năm học
  semester: 'fh025', // Học kỳ
  department: 'fn450', // Khoa
  major: 'fn500', // Ngành
  class: 'fb200', // Lớp
  student: 'fn100', // Sinh viên
  course: 'fh750', // Môn học
  grade_100: 'vn511', // Điể<PERSON> hệ 10
  grade_4: 'vn512', // Điể<PERSON> hệ 4
  grade_letter: 'vv513', // Điể<PERSON> chữ
  grade_10: 'vn514', // <PERSON>iể<PERSON> chữ

  deleted_by: 'vl544',
  deleted_at: 'vl545',
  updated_by: 'vl549',
  updated_at: 'vl548',
  created_by: 'vl547',
  created_at: 'vl546',
};

const schema = (sequelize, DataTypes) => {
  const studentGradeSchema = sequelize.define('StudentGrade', {
    [fields._id]: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    [fields.year]: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    [fields.semester]: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    [fields.department]: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    [fields.major]: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    [fields.class]: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    [fields.student]: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    [fields.course]: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    [fields.grade_100]: {
      type: DataTypes.DOUBLE,
      defaultValue: 0,
    },
    [fields.grade_4]: {
      type: DataTypes.DOUBLE,
      defaultValue: 0,
    },
    [fields.grade_letter]: {
      type: DataTypes.STRING(10),
      defaultValue: null,
    },
    [fields.grade_10]: {
      type: DataTypes.STRING(10),
      defaultValue: null,
    },

    [fields.deleted_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.deleted_at]: {
      type: DataTypes.DATEONLY,
      defaultValue: null,
    },
    [fields.created_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
    [fields.updated_by]: {
      type: DataTypes.STRING(128),
      defaultValue: null,
    },
  }, {
    tableName: fields.table,
    createdAt: fields.created_at,
    updatedAt: fields.updated_at,
  });

  const StudentGrade = studentGradeSchema;

  studentGradeSchema.get = async (id) => {
    try {
      const studentGrade = await StudentGrade.findOne({
        attributes: [
          [fields._id, '_id'],
          [fields.year, 'year'],
          [fields.semester, 'semester'],
          [fields.department, 'department'],
          [fields.major, 'major'],
          [fields.class, 'class'],
          [fields.student, 'student'],
          [fields.course, 'course'],
          [fields.grade_100, 'grade_100'],
          [fields.grade_4, 'grade_4'],
          [fields.grade_letter, 'grade_letter'],
          [fields.grade_10, 'grade_10'],
          [fields.created_by, 'created_by'],
        ],
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      return studentGrade;
    } catch (error) {
      throw error;
    }
  };

  studentGradeSchema.list = async ({
    page = 1,
    perPage = 30,
    order_by = fields._id,
    order_way = 'desc',
    student = { [Op.not]: null },
    year = { [Op.not]: null },
    semester = { [Op.not]: null },
    course = { [Op.not]: null },
    _class = { [Op.not]: null },
  }) => {
    page = parseInt(page, 10);
    perPage = parseInt(perPage, 10);
    let pagination = {};
    if (perPage > -1) {
      pagination = {
        offset: perPage * (page - 1),
        limit: perPage,
      };
    }
    const count = await StudentGrade.countItem({
      [fields.deleted_at]: null,
      [fields.student]: student,
      [fields.year]: year,
      [fields.semester]: semester,
      [fields.course]: course,
      [fields.class]: _class,
    });
    const studentGrade = await StudentGrade.findAll({
      attributes: [
        [fields._id, '_id'],
        [fields.year, 'year'],
        [fields.semester, 'semester'],
        [fields.department, 'department'],
        [fields.major, 'major'],
        [fields.class, 'class'],
        [fields.student, 'student'],
        [fields.course, 'course'],
        [fields.grade_100, 'grade_100'],
        [fields.grade_4, 'grade_4'],
        [fields.grade_letter, 'grade_letter'],
        [fields.grade_10, 'grade_10'],
      ],
      where: {
        [fields.deleted_at]: null,
        [fields.student]: student,
        [fields.year]: year,
        [fields.semester]: semester,
        [fields.course]: course,
        [fields.class]: _class,
      },
      ...pagination,
      order: [
        [order_by, order_way],
      ],
    });
    return { total: count, data: studentGrade };
  };

  studentGradeSchema.countItem = async (query) => {
    const count = await StudentGrade.count({
      where: {
        ...query,
      },
    });
    return count;
  };

  studentGradeSchema.patch = async ({ id, data }) => {
    try {
      const dbData = {};
      _.forEach(data, (value, key) => {
        dbData[fields[key]] = value;
      });
      await StudentGrade.update({ ...dbData }, {
        where: {
          [fields._id]: id,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  studentGradeSchema.remove = async ({ id, email }) => {
    try {
      const result = await StudentGrade.update({
        [fields.deleted_at]: moment().format(),
        [fields.deleted_by]: email,
      }, {
        where: {
          [fields._id]: id,
          [fields.deleted_at]: null,
        },
      });
      return result;
    } catch (error) {
      throw error;
    }
  };

  return studentGradeSchema;
};

module.exports = {
  schema,
  fields,
};

