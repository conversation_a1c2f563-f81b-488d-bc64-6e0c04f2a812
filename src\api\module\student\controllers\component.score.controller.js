// 18/11/2024
const _ = require('lodash');
const moment = require('moment');
const httpStatus = require('http-status');
const APIError = require('../../../utils/APIError');
const db = require('../../../../config/mysql');
const { handler: errorHandler } = require('../../../middlewares/error');
const { success: jsonSuccess } = require('../../../middlewares/success');
const { fields } = require('../models/component.score.model');

const ComponentScores = db.componentScores;
const Class = db.class;

exports.load = async (req, res, next, id) => {
  try {
    const componentScores = await ComponentScores.get({ id });
    req.locals = { componentScores };
    return next();
  } catch (error) {
    return errorHandler(error, req, res);
  }
};

exports.get = (req, res) => {
  jsonSuccess(req.locals.componentScores, req, res);
};

exports.create = async (req, res, next) => {
  try {
    const { user, body } = req;
    const _class = await Class.get(body.class);
    if (_class.dataValues.lecturer !== req.user._id) {
      throw new APIError({
        message: 'Don\'t have permission',
        status: httpStatus.FORBIDDEN,
      });
    }

    // Kiểm tra is_finish của class
    if (_class.dataValues.is_finish === 1) {
      throw new APIError({
        message: 'Cannot create or update component score. Class is already finished.',
        status: httpStatus.FORBIDDEN,
      });
    }

    // Kiểm tra xem ComponentScore đã tồn tại chưa
    const existingScore = await ComponentScores.findOne({
      where: {
        [fields.grade_component]: body.grade_component,
        [fields.class]: body.class,
        [fields.student]: body.student,
        [fields.deleted_at]: null,
      },
    });

    if (existingScore) {
      // Nếu đã tồn tại thì update
      const updateData = { ...body };
      updateData.updated_by = user.email;
      updateData.updated_at = moment().format();

      await ComponentScores.patch({
        id: existingScore.dataValues[fields._id],
        data: updateData,
      });

      // Lấy dữ liệu đã update để trả về
      const updatedScore = await ComponentScores.get({ id: existingScore.dataValues[fields._id] });
      jsonSuccess(updatedScore, req, res);
    } else {
      // Nếu chưa tồn tại thì tạo mới
      req.body.created_by = user.email;
      const dbData = {};
      _.forEach(body, (value, key) => {
        dbData[fields[key]] = value;
      });
      const componentScores = ComponentScores.build({
        ...dbData,
      });
      const saved = await componentScores.save();
      jsonSuccess(saved, req, res);
    }
  } catch (error) {
    next(error);
  }
};

exports.update = async (req, res, next) => {
  try {
    const _class = await Class.get(req.locals.componentScores.dataValues.class);
    if (_class.dataValues.lecturer !== req.user._id) {
      throw new APIError({
        message: 'Don\'t have permission',
        status: httpStatus.FORBIDDEN,
      });
    }

    // Kiểm tra is_finish của class
    if (_class.dataValues.is_finish === 1) {
      throw new APIError({
        message: 'Cannot create or update component score. Class is already finished.',
        status: httpStatus.FORBIDDEN,
      });
    }
    const { user } = req;
    const data = req.body;
    data.updated_by = user.email;
    data.updated_at = moment().format();
    await ComponentScores.patch({
      id: req.params.id,
      data: req.body,
    });
    jsonSuccess({}, req, res);
  } catch (error) {
    next(error);
  }
};

exports.list = async (req, res, next) => {
  try {
    const componentScores = await ComponentScores.list(req.query);
    jsonSuccess(componentScores, req, res);
  } catch (error) {
    next(error);
  }
};

exports.remove = async (req, res, next) => {
  try {
    const _class = await Class.get(req.locals.componentScores.dataValues.class);
    if (_class.dataValues.lecturer !== req.user._id) {
      throw new APIError({
        message: 'Don\'t have permission',
        status: httpStatus.FORBIDDEN,
      });
    }
    const result = await ComponentScores.remove({
      id: req.params.id,
      email: req.user.email,
    });
    jsonSuccess({ result }, req, res);
  } catch (error) {
    next(error);
  }
};
